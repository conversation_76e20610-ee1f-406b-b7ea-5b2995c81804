package agent_referral

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type SnapshotTask struct {
	repo repo.InvitationRepo
}

func NewSnapshotTask(repo repo.InvitationRepo) *SnapshotTask {
	return &SnapshotTask{
		repo: repo,
	}
}

// UpdateAllReferralSnapshots updates agent referral snapshot statistics for all users
// This includes direct count, total downline count, total volume USD, total rewards distributed, and upline relationships
func (s *SnapshotTask) UpdateAllReferralSnapshots() {
	global.GVA_LOG.Info("Starting agent referral snapshot update for all users")

	var userIDs []uuid.UUID
	global.GVA_DB.Model(&model.User{}).Pluck("id", &userIDs)

	global.GVA_LOG.Info("Found users to process", zap.Int("user_count", len(userIDs)))

	processedCount := 0
	errorCount := 0

	for _, userID := range userIDs {
		if err := s.updateUserReferralSnapshot(userID); err != nil {
			global.GVA_LOG.Error("Failed to update referral snapshot for user",
				zap.String("user_id", userID.String()),
				zap.Error(err))
			errorCount++
		} else {
			processedCount++
		}
	}

	global.GVA_LOG.Info("Referral snapshot update completed",
		zap.Int("total_users", len(userIDs)),
		zap.Int("processed_count", processedCount),
		zap.Int("error_count", errorCount))
}

// updateUserReferralSnapshot updates referral snapshot for a single user
func (s *SnapshotTask) updateUserReferralSnapshot(userID uuid.UUID) error {
	// global.GVA_LOG.Debug("Processing referral snapshot for user", zap.String("user_id", userID.String()))

	// Calculate direct referral count (depth = 1)
	var directCount int64
	global.GVA_DB.Model(&model.Referral{}).
		Where("referrer_id = ? AND depth = 1", userID).
		Count(&directCount)

	// Calculate total downline count (depth <= 3)
	var totalDownlineCount int64
	global.GVA_DB.Model(&model.Referral{}).
		Where("referrer_id = ? AND depth <= 3", userID).
		Count(&totalDownlineCount)

	// TradingUserCount How many addresses of the invitees invited by the current user have conducted MEME or contract transactions.
	//  As long as one of the transactions is completed, it is considered as the number of transactions.
	var tradingUserCount int64
	err := global.GVA_DB.Model(&model.Referral{}).
		Joins("LEFT JOIN hyper_liquid_transactions hlt ON referrals.user_id = hlt.user_id AND hlt.status = 'filled'").
		Joins("LEFT JOIN affiliate_transactions at ON referrals.user_id = at.user_id AND at.status = 'completed'").
		Where("referrals.referrer_id = ? AND (hlt.user_id IS NOT NULL OR at.user_id IS NOT NULL)", userID).
		Distinct("referrals.user_id").
		Count(&tradingUserCount).Error
	if err != nil {
		return err
	}

	// TotalPerpsVolumeUSD = avg_price * totalSz, status = filled
	var totalPerpsVolumeUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.HyperLiquidTransaction{}).
		Joins("JOIN referrals ON hyper_liquid_transactions.user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND hyper_liquid_transactions.status = ?", userID, "filled").
		Select("COALESCE(SUM(hyper_liquid_transactions.avg_price * hyper_liquid_transactions.size), 0)").
		Scan(&totalPerpsVolumeUSD).Error
	if err != nil {
		return err
	}

	// TotalPerps FeesQuery the transaction volume of HyperLiquidTransaction with BuildFee as the handling fee and status as filled
	var totalPerpsFees decimal.Decimal
	err = global.GVA_DB.Model(&model.HyperLiquidTransaction{}).
		Joins("JOIN referrals ON hyper_liquid_transactions.user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND hyper_liquid_transactions.status = ?", userID, "filled").
		Select("COALESCE(SUM(hyper_liquid_transactions.build_fee), 0)").
		Scan(&totalPerpsFees).Error
	if err != nil {
		return err
	}

	// TotalPerpsFeesPaid Query CommissionAmount in CommissionLedger, the commission amount with status CLAIMED, and calculate the total commission
	var totalPerpsFeesPaid decimal.Decimal
	err = global.GVA_DB.Model(&model.CommissionLedger{}).
		Joins("JOIN referrals ON commission_ledger.source_user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND commission_ledger.status = ?", userID, "CLAIMED").
		Select("COALESCE(SUM(commission_ledger.commission_amount), 0)").
		Scan(&totalPerpsFeesPaid).Error
	if err != nil {
		return err
	}

	// TotalPerpsFeesUnPaid  The commission amount of the PENDING_CLAIM status, calculate the total commission
	var totalPerpsFeesUnPaid decimal.Decimal
	err = global.GVA_DB.Model(&model.CommissionLedger{}).
		Joins("JOIN referrals ON commission_ledger.source_user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND commission_ledger.status = ?", userID, "PENDING_CLAIM").
		Select("COALESCE(SUM(commission_ledger.commission_amount), 0)").
		Scan(&totalPerpsFeesUnPaid).Error
	if err != nil {
		return err
	}

	// TotalPerpsTradesCount How many addresses of the invitees invited by the current user have conducted contract transactions.
	// As long as one of the transactions is completed, it is considered as the number of transactions.
	var totalPerpsTradesCount int64
	err = global.GVA_DB.Model(&model.HyperLiquidTransaction{}).
		Joins("JOIN referrals ON hyper_liquid_transactions.user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND hyper_liquid_transactions.status = ?", userID, "filled").
		Distinct("hyper_liquid_transactions.user_id").
		Count(&totalPerpsTradesCount).Error
	if err != nil {
		return err
	}

	// TotalMemeVolumeUSD  SolPriceUSD * CashbackAmountSOL
	var totalMemeVolumeUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.ActivityCashback{}).
		Joins("JOIN referrals ON activity_cashback.user_id = referrals.user_id").
		Where("referrals.referrer_id = ?", userID).
		Select("COALESCE(SUM(activity_cashback.sol_price_usd * activity_cashback.cashback_amount_sol), 0)").
		Scan(&totalMemeVolumeUSD).Error
	if err != nil {
		return err
	}

	// TotalMemeFees Query the CashbackAmountUSD of ActivityCashback
	var totalMemeFees decimal.Decimal
	err = global.GVA_DB.Model(&model.ActivityCashback{}).
		Joins("JOIN referrals ON activity_cashback.user_id = referrals.user_id").
		Where("referrals.referrer_id = ?", userID).
		Select("COALESCE(SUM(activity_cashback.cashback_amount_usd), 0)").
		Scan(&totalMemeFees).Error
	if err != nil {
		return err
	}

	// TotalMemeFeesPaid Query the CashbackAmountUSD of ActivityCashback, the commission amount of the CLAIMED status, and calculate the total commission
	var totalMemeFeesPaid decimal.Decimal
	err = global.GVA_DB.Model(&model.ActivityCashback{}).
		Joins("JOIN referrals ON activity_cashback.user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND activity_cashback.status = ?", userID, "CLAIMED").
		Select("COALESCE(SUM(activity_cashback.cashback_amount_usd), 0)").
		Scan(&totalMemeFeesPaid).Error
	if err != nil {
		return err
	}

	// TotalMemeFeesUnPaid The commission amount of the PENDING_CLAIM status, calculate the total commission
	var totalMemeFeesUnPaid decimal.Decimal
	err = global.GVA_DB.Model(&model.CommissionLedger{}).
		Joins("JOIN referrals ON commission_ledger.source_user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND commission_ledger.status = ?", userID, "PENDING_CLAIM").
		Select("COALESCE(SUM(commission_ledger.commission_amount), 0)").
		Scan(&totalMemeFeesUnPaid).Error
	if err != nil {
		return err
	}

	// TotalMemeTradesCount How many addresses of the invitees invited by the current user have conducted Meme transactions.
	//  As long as one of the transactions is completed, it is considered as the number of transactions.
	var totalMemeTradesCount int64
	err = global.GVA_DB.Model(&model.ActivityCashback{}).
		Joins("JOIN referrals ON activity_cashback.user_id = referrals.user_id").
		Where("referrals.referrer_id = ?", userID).
		Distinct("activity_cashback.user_id").
		Count(&totalMemeTradesCount).Error
	if err != nil {
		return err
	}

	// TotalCommissionEarnedUSD The user queries the CommissionAmount field in the CommissionLedger table to find the total amount of commission earned.
	var totalCommissionEarnedUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.CommissionLedger{}).
		Where("recipient_user_id = ?", userID).
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&totalCommissionEarnedUSD).Error
	if err != nil {
		return err
	}

	// ClaimedCommissionUSD Query the CommissionAmount field of the CommissionLedger table, the commission amount of the CLAIMED status, and calculate the total commission
	var claimedCommissionUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.CommissionLedger{}).
		Where("recipient_user_id = ? AND status = ?", userID, "CLAIMED").
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&claimedCommissionUSD).Error
	if err != nil {
		return err
	}

	// UnclaimedCommissionUSD Query the CommissionAmount field of the CommissionLedger table,
	// the commission amount with the status of PENDING_CLAIM, and calculate the total commission
	var unclaimedCommissionUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.CommissionLedger{}).
		Where("recipient_user_id = ? AND status = ?", userID, "PENDING_CLAIM").
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&unclaimedCommissionUSD).Error
	if err != nil {
		return err
	}

	// TotalCashbackEarnedUSD Query the CashbackAmountUSD field of ActivityCashback to calculate the total rebate
	var totalCashbackEarnedUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.ActivityCashback{}).
		Where("user_id = ?", userID).
		Select("COALESCE(SUM(cashback_amount_usd), 0)").
		Scan(&totalCashbackEarnedUSD).Error
	if err != nil {
		return err
	}

	// ClaimedCashbackUSD Query the CashbackAmountUSD field of ActivityCashback,
	// the commission amount of the CLAIMED status, and calculate the total commission
	var claimedCashbackUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.ActivityCashback{}).
		Where("user_id = ? AND status = ?", userID, "CLAIMED").
		Select("COALESCE(SUM(cashback_amount_usd), 0)").
		Scan(&claimedCashbackUSD).Error
	if err != nil {
		return err
	}

	// UnclaimedCashbackUSD Query the CashbackAmountUSD field of ActivityCashback,
	// the commission amount of the status PENDING_CLAIM, and calculate the total commission
	var unclaimedCashbackUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.ActivityCashback{}).
		Where("user_id = ? AND status = ?", userID, "PENDING_CLAIM").
		Select("COALESCE(SUM(cashback_amount_usd), 0)").
		Scan(&unclaimedCashbackUSD).Error
	if err != nil {
		return err
	}

	// Get upline relationships (L1, L2, L3)
	var l1UplineID, l2UplineID, l3UplineID *uuid.UUID
	var referral model.Referral
	err = global.GVA_DB.Where("user_id = ?", userID).First(&referral).Error
	if err == nil && referral.ReferrerID != nil {
		// L1 upline is the direct referrer
		l1UplineID = referral.ReferrerID

		// Find L2 upline (referrer of L1)
		var l1Referral model.Referral
		err = global.GVA_DB.Where("user_id = ?", *l1UplineID).First(&l1Referral).Error
		if err == nil && l1Referral.ReferrerID != nil {
			l2UplineID = l1Referral.ReferrerID

			// Find L3 upline (referrer of L2)
			var l2Referral model.Referral
			err = global.GVA_DB.Where("user_id = ?", *l2UplineID).First(&l2Referral).Error
			if err == nil && l2Referral.ReferrerID != nil {
				l3UplineID = l2Referral.ReferrerID
			}
		}
	}

	// Create or update ReferralSnapshot
	var snapshot model.ReferralSnapshot
	err = global.GVA_DB.Where("user_id = ?", userID).First(&snapshot).Error
	if err == gorm.ErrRecordNotFound {
		// Create new snapshot
		snapshot = model.ReferralSnapshot{
			UserID:                   userID,
			DirectCount:              int(directCount),
			TotalDownlineCount:       int(totalDownlineCount),
			TradingUserCount:         int(tradingUserCount),
			TotalPerpsVolumeUSD:      totalPerpsVolumeUSD,
			TotalPerpsFees:           totalPerpsFees,
			TotalPerpsFeesPaid:       totalPerpsFeesPaid,
			TotalPerpsFeesUnPaid:     totalPerpsFeesUnPaid,
			TotalPerpsTradesCount:    totalPerpsTradesCount,
			TotalMemeVolumeUSD:       totalMemeVolumeUSD,
			TotalMemeFees:            totalMemeFees,
			TotalMemeFeesPaid:        totalMemeFeesPaid,
			TotalMemeFeesUnPaid:      totalMemeFeesUnPaid,
			TotalMemeTradesCount:     totalMemeTradesCount,
			TotalCommissionEarnedUSD: totalCommissionEarnedUSD,
			ClaimedCommissionUSD:     claimedCommissionUSD,
			UnclaimedCommissionUSD:   unclaimedCommissionUSD,
			TotalCashbackEarnedUSD:   totalCashbackEarnedUSD,
			ClaimedCashbackUSD:       claimedCashbackUSD,
			UnclaimedCashbackUSD:     unclaimedCashbackUSD,
			L1UplineID:               l1UplineID,
			L2UplineID:               l2UplineID,
			L3UplineID:               l3UplineID,
		}
		err = global.GVA_DB.Create(&snapshot).Error
		if err != nil {
			return err
		}
		return nil
	} else if err == nil {
		// Update existing snapshot
		updates := model.ReferralSnapshot{
			DirectCount:              int(directCount),
			TotalDownlineCount:       int(totalDownlineCount),
			TradingUserCount:         int(tradingUserCount),
			TotalPerpsVolumeUSD:      totalPerpsVolumeUSD,
			TotalPerpsFees:           totalPerpsFees,
			TotalPerpsFeesPaid:       totalPerpsFeesPaid,
			TotalPerpsFeesUnPaid:     totalPerpsFeesUnPaid,
			TotalPerpsTradesCount:    totalPerpsTradesCount,
			TotalMemeVolumeUSD:       totalMemeVolumeUSD,
			TotalMemeFees:            totalMemeFees,
			TotalMemeFeesPaid:        totalMemeFeesPaid,
			TotalMemeFeesUnPaid:      totalMemeFeesUnPaid,
			TotalMemeTradesCount:     totalMemeTradesCount,
			TotalCommissionEarnedUSD: totalCommissionEarnedUSD,
			ClaimedCommissionUSD:     claimedCommissionUSD,
			UnclaimedCommissionUSD:   unclaimedCommissionUSD,
			TotalCashbackEarnedUSD:   totalCashbackEarnedUSD,
			ClaimedCashbackUSD:       claimedCashbackUSD,
			UnclaimedCashbackUSD:     unclaimedCashbackUSD,
			L1UplineID:               l1UplineID,
			L2UplineID:               l2UplineID,
			L3UplineID:               l3UplineID,
		}
		err = global.GVA_DB.Model(&snapshot).Updates(updates).Error
		if err != nil {
			return err
		}
	} else {
		return err
	}

	return nil
}

